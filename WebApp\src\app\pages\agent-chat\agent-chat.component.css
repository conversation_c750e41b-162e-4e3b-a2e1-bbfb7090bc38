/* Scrollbar styles for better UX */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #4a4a4a;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* Drawer styles for sidebars */
.ant-drawer-body {
  padding: 0;
  padding-left: 5px !important;
}

/* Markdown formatting for chat responses */
:host ::ng-deep .response-container {
  /* Typography */
  font-size: 1rem;
  line-height: 1.6;

  /* Element spacing */
  p {
    margin-bottom: 1rem;
  }

  h1, h2, h3, h4, h5, h6 {
    margin-top: 1.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
    color: var(--text-dark);
  }

  /* Lists */
  ul, ol {
    padding-left: 1.5rem;
    margin-bottom: 1rem;
  }

  li {
    margin-bottom: 0.25rem;
  }

  /* Code formatting */
  code {
    background-color: var(--hover-blue-gray);
    padding: 0.2rem 0.4rem;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.9em;
  }

  pre {
    background-color: var(--hover-blue-gray);
    padding: 1rem;
    border-radius: var(--border-radius-small);
    overflow-x: auto;
    margin: 1rem 0;
  }

  pre code {
    background-color: transparent;
    padding: 0;
    color: var(--text-dark);
  }

  /* Tables */
  table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
  }

  th, td {
    border: 1px solid var(--hover-blue-gray);
    padding: 0.5rem;
  }

  th {
    background-color: var(--hover-blue-gray);
    font-weight: 600;
  }

  /* Links */
  a {
    color: var(--primary-purple);
    text-decoration: underline;
    text-decoration-thickness: 1px;
  }

  a:hover {
    color: var(--secondary-purple);
  }

  /* Blockquotes */
  blockquote {
    border-left: 4px solid var(--hover-blue-gray);
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    color: var(--text-medium-gray);
  }
}

::ng-deep code {
  text-wrap: auto;
}

/* Utility classes for chat component */
.width-indicator {
  position: absolute;
  top: 5px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--primary-purple);
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 100;
}

/* Chat UI specific styles */
.right-sidebar-area, .main-content-area {
  transition: width 0.3s ease;
}

/* Animation for loading dots */
@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* Scrollable areas */
.sidebar-container, .chat-container {
  height: 100%;
  overflow-y: auto;
}

/* Response animation */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* ChatGPT-style action buttons */
.action-buttons-container {
  transition: opacity 0.2s ease-in-out;
}

.action-buttons-container:hover {
  opacity: 1;
}

/* Sources button styling */
.sources-button {
  background: var(--background-white);
  border: 1px solid var(--hover-blue-gray);
  border-radius: var(--border-radius-small);
  transition: all 0.2s ease;
}

.sources-button:hover {
  background: var(--hover-blue-gray);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Action button group styling */
.action-button-group {
  background: var(--background-white);
  border: 1px solid var(--hover-blue-gray);
  border-radius: 8px;
  padding: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.action-button-group:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Individual action buttons */
.action-button {
  padding: 6px;
  border-radius: var(--border-radius-small);
  transition: all 0.15s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-button:hover {
  background: var(--hover-blue-gray);
  transform: scale(1.05);
}

/* Source count badge */
.source-count-badge {
  background: var(--primary-purple);
  color: white;
  border-radius: 50%;
  padding: 2px 6px;
  font-size: 10px;
  font-weight: 600;
  min-width: 18px;
  text-align: center;
}

/* Dark theme support for ChatGPT-style buttons */
:host-context(.dark-theme) .action-button-group {
  background: var(--dialog-bg);
  border-color: var(--hover-blue-gray);
}

:host-context(.dark-theme) .action-button:hover {
  background: var(--hover-blue-gray);
}

:host-context(.dark-theme) .sources-button {
  background: var(--dialog-bg);
  border-color: var(--hover-blue-gray);
}

:host-context(.dark-theme) .sources-button:hover {
  background: var(--hover-blue-gray);
}

/* Responsive design for action buttons */
@media (max-width: 640px) {
  .action-button-group {
    flex-wrap: wrap;
    gap: 2px;
  }

  .action-button {
    padding: 4px;
  }

  .action-button span {
    display: none;
  }

  .source-count-badge {
    font-size: 8px;
    padding: 1px 4px;
    min-width: 14px;
  }
}

.response-container {
  animation: fadeIn 0.3s ease-in;
}

/* Hover effects for buttons */
button {
  transition: all 0.2s ease;
}

/* Dark mode adjustments */
:host-context(.dark) .response-container {
  color: #e0e0e0;
}

:host-context(.dark) code {
  background-color: #2a2a2a;
  color: #e0e0e0;
}

:host-context(.dark) pre {
  background-color: #1e1e1e;
  border: 1px solid #333;
}
